import requests,json

url = "https://api.braintrust.dev/v1/eval"
headers = {
    "Authorization": "Bearer sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU",
    "Content-Type": "application/json"
}

payload = {
    "project_id": "4542abd4-537e-4ca1-b5a4-14964fe42b78",
    "data": {
        "dataset_id": "f9be2853-fba3-4ee7-8f62-0d371098e816",
        "_internal_btql": {
            "sort": [
                {
                    "expr": {
                        "op": "ident",
                        "name": ["created"]
                    },
                    "dir": "desc"
                },
                {
                    "expr": {
                        "op": "ident",
                        "name": ["id"]
                    },
                    "dir": "desc"
                }
            ]
        }
    },
    "task": {
        "name": "prompt1",
        "inline_prompt": {
            "prompt": {
                "type": "chat",
                "messages": [
                    {
                        "content": "Based on the following description, identify the movie title. In your response, simply provide the name of the movie.",
                        "role": "system"
                    },
                    {
                        "content": [
                            {
                                "text": "{{input}}",
                                "type": "text"
                            }
                        ],
                        "role": "user"
                    }
                ]
            },
            "options": {
                "model": "gpt-4o",
                "params": {
                    "use_cache": True,
                    "temperature": 0
                }
            }
        },
        "inline_function": {
            "type": "prompt"
        },
        "metadata": {
            "model": "gpt-4o",
            "use_cache": True,
            "temperature": 0
        }
    },
    "scores": [
        {
            "global_function": "ExactMatch"
        }
    ],
    "experiment_name": "triggered_from_api2",
    "metadata": {
        "model": "gpt-4o",
        "use_cache": True,
        "temperature": 0
    },
    "stream": True,
    "trial_count": 1,
    "is_public": False,
    "timeout": 0,
    "max_concurrency": 10,
    "stop_token": "stop_function:f894a22b-235c-4866-b40f-2321ee4a6893:2d68ba9e-099c-4aab-a952-1e28b729c40b"
}

response = requests.post(url, headers=headers, json=payload, stream=True)

# Capture flag
capture_summary = False
summary_lines = []

for line in response.iter_lines():
    if not line:
        continue
    decoded_line = line.decode('utf-8').strip()

    # Detect the event line
    if decoded_line.startswith("event: summary"):
        capture_summary = True
        summary_lines.append(decoded_line)
        continue

    # If we just saw "event: summary", capture the following line(s)
    if capture_summary:
        summary_lines.append(decoded_line)
        # If it's standard SSE format, the next line after data is blank, so break
        if decoded_line == "":
            break

summary = json.loads(summary_lines.removeprefix("data: ").strip())
print(summary)